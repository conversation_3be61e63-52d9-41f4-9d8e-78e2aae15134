import { supabaseClient } from './src/database/supabase.js';
import { DownloadWorker } from './src/services/download-worker.js';
import fs from 'fs';
import path from 'path';

/**
 * Test the new database-based file copy logic
 */
async function testDatabaseCopyLogic() {
    try {
        console.log('🧪 Testing database-based file copy logic...');
        
        const client = supabaseClient.getServiceClient();
        
        // Create a mock download worker
        const worker = new DownloadWorker('test-session-id', {
            download_path: './test-downloads',
            concurrent_downloads: 1
        });
        
        // Test 1: Find files that could be copied (same file_id)
        console.log('\n📋 Test 1: Looking for files with same file_id across users...');
        
        const { data: allFiles, error } = await client
            .from('scanned_files_cuong')
            .select('file_id, name, user_email, full_path, local_path, size, mime_type')
            .limit(100);
            
        if (error) {
            console.error('❌ Error querying files:', error.message);
            return;
        }
        
        // Group by file_id to find potential shared files
        const fileGroups = {};
        allFiles.forEach(file => {
            if (!fileGroups[file.file_id]) {
                fileGroups[file.file_id] = [];
            }
            fileGroups[file.file_id].push(file);
        });
        
        const sharedFileIds = Object.entries(fileGroups)
            .filter(([fileId, files]) => files.length > 1)
            .slice(0, 3); // Get first 3 for testing
            
        console.log(`Found ${sharedFileIds.length} file groups with multiple users`);
        
        // Test 2: Simulate the copy logic
        if (sharedFileIds.length > 0) {
            const [testFileId, testFiles] = sharedFileIds[0];
            console.log(`\n🎯 Testing with file_id: ${testFileId}`);
            console.log(`   File name: ${testFiles[0].name}`);
            console.log(`   Users: ${testFiles.map(f => f.user_email).join(', ')}`);
            
            // Create a mock download item for the second user
            const downloadItem = {
                id: 'test-download-item',
                file_id: testFileId,
                name: testFiles[0].name,
                user_email: testFiles[1].user_email, // Use second user
                full_path: testFiles[1].full_path,
                size: testFiles[1].size,
                mime_type: testFiles[1].mime_type
            };
            
            // Test the findCopyableFileInDatabase method
            console.log(`\n🔍 Testing findCopyableFileInDatabase for user: ${downloadItem.user_email}`);
            const copyableFile = await worker.findCopyableFileInDatabase(downloadItem);
            
            if (copyableFile) {
                console.log(`✅ Found copyable file:`);
                console.log(`   Source user: ${copyableFile.userEmail}`);
                console.log(`   Local path: ${copyableFile.localPath}`);
                console.log(`   Method: ${copyableFile.method}`);
                console.log(`   File size: ${copyableFile.fileSize}`);
            } else {
                console.log(`❌ No copyable file found (this is expected if no files have been downloaded yet)`);
            }
        }
        
        // Test 3: Check for similar files (same name + size + mime_type)
        console.log('\n📋 Test 3: Looking for similar files across users...');
        
        const nameGroups = {};
        allFiles.forEach(file => {
            const key = `${file.name}_${file.size}_${file.mime_type}`;
            if (!nameGroups[key]) {
                nameGroups[key] = [];
            }
            nameGroups[key].push(file);
        });
        
        const similarFileGroups = Object.entries(nameGroups)
            .filter(([key, files]) => {
                // Check if files are from different users
                const uniqueUsers = new Set(files.map(f => f.user_email));
                return uniqueUsers.size > 1;
            })
            .slice(0, 3);
            
        console.log(`Found ${similarFileGroups.length} file groups with similar files across users`);
        
        if (similarFileGroups.length > 0) {
            const [testKey, testFiles] = similarFileGroups[0];
            console.log(`\n🎯 Testing similar file: ${testKey}`);
            console.log(`   File name: ${testFiles[0].name}`);
            console.log(`   Users: ${testFiles.map(f => f.user_email).join(', ')}`);
        }
        
        // Test 4: Show current database state
        console.log('\n📊 Current database state:');
        
        const { data: downloadedFiles, error: downloadedError } = await client
            .from('scanned_files_cuong')
            .select('user_email, name, local_path')
            .not('local_path', 'is', null)
            .limit(5);
            
        if (downloadedError) {
            console.error('❌ Error querying downloaded files:', downloadedError.message);
        } else {
            console.log(`   Downloaded files: ${downloadedFiles.length}`);
            downloadedFiles.forEach((file, index) => {
                console.log(`   ${index + 1}. ${file.name} (${file.user_email}) -> ${file.local_path}`);
            });
        }
        
        console.log('\n✅ Database copy logic test completed');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testDatabaseCopyLogic()
    .then(() => {
        console.log('\n🎉 Test completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test script failed:', error.message);
        process.exit(1);
    });
