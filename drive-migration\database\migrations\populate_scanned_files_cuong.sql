-- Migration: Populate scanned_files_cuong table with data from scanned_files
-- Date: 2025-07-16
-- Description: Copy data from scanned_files to scanned_files_cuong for the new download workflow

-- First, ensure the scanned_files_cuong table exists
-- (This should have been created by the create_scanned_files_cuong.sql migration)

-- Insert data from scanned_files into scanned_files_cuong
-- Only insert records that don't already exist to avoid duplicates
INSERT INTO scanned_files_cuong (
    scan_session_id,
    file_id,
    name,
    mime_type,
    size,
    full_path,
    depth,
    parents,
    created_time,
    modified_time,
    owners,
    permissions,
    web_view_link,
    metadata,
    is_selected,
    user_email,
    domain,
    download_status,
    local_path,
    downloaded_at,
    upload_status,
    lark_file_token,
    lark_folder_token,
    uploaded_at,
    created_at
)
SELECT 
    sf.scan_session_id,
    sf.file_id,
    sf.name,
    sf.mime_type,
    sf.size,
    sf.full_path,
    sf.depth,
    sf.parents,
    sf.created_time,
    sf.modified_time,
    sf.owners,
    sf.permissions,
    sf.web_view_link,
    sf.metadata,
    sf.is_selected,
    sf.user_email,
    sf.domain,
    -- Map download_status from scanned_files to scanned_files_cuong
    CASE 
        WHEN sf.download_status IS NULL THEN 'not_downloaded'
        WHEN sf.download_status = 'not_downloaded' THEN 'not_downloaded'
        WHEN sf.download_status = 'downloaded' THEN 'downloaded'
        WHEN sf.download_status = 'failed' THEN 'failed'
        WHEN sf.download_status = 'exposed' THEN 'downloaded' -- Treat exposed as downloaded
        ELSE 'not_downloaded'
    END as download_status,
    sf.local_path,
    sf.downloaded_at,
    -- Map upload_status from scanned_files to scanned_files_cuong
    CASE 
        WHEN sf.upload_status IS NULL THEN 'not_uploaded'
        WHEN sf.upload_status = 'not_uploaded' THEN 'not_uploaded'
        WHEN sf.upload_status = 'uploading' THEN 'uploading'
        WHEN sf.upload_status = 'uploaded' THEN 'uploaded'
        WHEN sf.upload_status = 'failed' THEN 'failed'
        ELSE 'not_uploaded'
    END as upload_status,
    sf.lark_file_token,
    sf.lark_folder_token,
    sf.uploaded_at,
    sf.created_at
FROM scanned_files sf
WHERE NOT EXISTS (
    -- Avoid duplicates by checking if file_id already exists in scanned_files_cuong
    SELECT 1 FROM scanned_files_cuong sfc 
    WHERE sfc.file_id = sf.file_id
);

-- Create indexes if they don't exist (should already be created by the table creation script)
-- These are here as a safety measure
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_user_email ON scanned_files_cuong(user_email);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_file_id ON scanned_files_cuong(file_id);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_download_status ON scanned_files_cuong(download_status);

-- Update statistics
DO $$
DECLARE
    total_records INTEGER;
    copied_records INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_records FROM scanned_files;
    SELECT COUNT(*) INTO copied_records FROM scanned_files_cuong;
    
    RAISE NOTICE 'Migration completed:';
    RAISE NOTICE '  - Total records in scanned_files: %', total_records;
    RAISE NOTICE '  - Total records in scanned_files_cuong: %', copied_records;
    RAISE NOTICE '  - Records copied in this migration: %', copied_records;
END $$;
