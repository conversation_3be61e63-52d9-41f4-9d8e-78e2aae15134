-- Migration: Create scanned_files_cuong table for new download workflow
-- Date: 2025-07-16
-- Description: Create scanned_files_cuong table to replace the download_items workflow

-- Create scanned_files_cuong table
CREATE TABLE IF NOT EXISTS scanned_files_cuong (
  id BIGSERIAL PRIMARY KEY,
  scan_session_id UUID REFERENCES scan_sessions(id) ON DELETE CASCADE,
  file_id TEXT NOT NULL, -- Google Drive file ID
  name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT DEFAULT 0,
  full_path TEXT NOT NULL, -- Full path in Google Drive
  depth INTEGER NOT NULL DEFAULT 0,
  parents TEXT[], -- Array of parent folder IDs
  created_time TIMESTAMPTZ,
  modified_time TIMESTAMPTZ,
  owners JSONB, -- Google Drive owners info
  permissions JSONB, -- Google Drive permissions
  web_view_link TEXT,
  metadata JSONB, -- Additional metadata
  is_selected BOOLEAN DEFAULT FALSE,
  user_email TEXT NOT NULL,
  domain TEXT NOT NULL,
  
  -- Download tracking fields
  download_status TEXT DEFAULT 'not_downloaded' CHECK (download_status IN ('not_downloaded', 'downloading', 'downloaded', 'failed', 'skipped')),
  local_path TEXT, -- Path where file was downloaded locally
  downloaded_at TIMESTAMPTZ, -- When the file was downloaded
  download_error_message TEXT, -- Error message if download failed
  
  -- Upload tracking fields (for future use)
  upload_status TEXT DEFAULT 'not_uploaded' CHECK (upload_status IN ('not_uploaded', 'uploading', 'uploaded', 'failed')) DEFAULT 'not_uploaded',
  lark_file_token TEXT,
  lark_folder_token TEXT,
  uploaded_at TIMESTAMPTZ,
  upload_error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_user_email ON scanned_files_cuong(user_email);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_file_id ON scanned_files_cuong(file_id);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_full_path ON scanned_files_cuong(full_path);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_download_status ON scanned_files_cuong(download_status);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_mime_type ON scanned_files_cuong(mime_type);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_size ON scanned_files_cuong(size);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_scan_session ON scanned_files_cuong(scan_session_id);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_domain ON scanned_files_cuong(domain);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_user_download_status ON scanned_files_cuong(user_email, download_status);
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_user_mime_type ON scanned_files_cuong(user_email, mime_type);

-- Add comments
COMMENT ON TABLE scanned_files_cuong IS 'Enhanced scanned files table for new download workflow - replaces download_items approach';
COMMENT ON COLUMN scanned_files_cuong.file_id IS 'Google Drive file ID';
COMMENT ON COLUMN scanned_files_cuong.full_path IS 'Full path in Google Drive including folder hierarchy';
COMMENT ON COLUMN scanned_files_cuong.download_status IS 'Current download status of the file';
COMMENT ON COLUMN scanned_files_cuong.local_path IS 'Local file system path where file was downloaded';
COMMENT ON COLUMN scanned_files_cuong.user_email IS 'Email of the user who owns this file in Google Drive';

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_scanned_files_cuong_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_scanned_files_cuong_updated_at
    BEFORE UPDATE ON scanned_files_cuong
    FOR EACH ROW
    EXECUTE FUNCTION update_scanned_files_cuong_updated_at();
