/**
 * Test script để verify logic download mới
 * - Session tạo với tất cả files (không filter theo download_status)
 * - Download skip file đã tồn tại với size tương tự (chênh lệch <= 10%)
 * - Tạo suffix cho file có size khác nhau (chênh lệch > 10%)
 */

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const API_BASE = 'http://localhost:3001/api';
const TEST_DOWNLOAD_PATH = path.join(__dirname, 'test-downloads-new-logic');

// Test data
const TEST_USER_EMAIL = '<EMAIL>';

async function main() {
    console.log('🧪 Testing new download logic...\n');
    
    try {
        // Step 1: Test session creation with all files
        console.log('📋 Step 1: Testing session creation with all files');
        await testSessionCreationWithAllFiles();
        
        // Step 2: Test file conflict handling
        console.log('\n📋 Step 2: Testing file conflict handling');
        await testFileConflictHandling();
        
        // Step 3: Test suffix generation
        console.log('\n📋 Step 3: Testing suffix generation');
        await testSuffixGeneration();
        
        console.log('\n✅ All tests completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

/**
 * Test session creation includes all files regardless of download_status
 */
async function testSessionCreationWithAllFiles() {
    try {
        // Get users with files
        const usersResponse = await axios.get(`${API_BASE}/download/available-users`);
        const users = usersResponse.data.data;
        
        if (users.length === 0) {
            console.log('⚠️ No users with files found, skipping session creation test');
            return;
        }
        
        const testUser = users[0];
        console.log(`   👤 Testing with user: ${testUser.user_email} (${testUser.file_count} files)`);
        
        // Create session
        const sessionConfig = {
            name: `Test New Logic Session - ${new Date().toISOString()}`,
            selectedUsers: [testUser.user_email],
            downloadPath: TEST_DOWNLOAD_PATH,
            concurrentDownloads: 1,
            maxRetries: 1
        };
        
        const createResponse = await axios.post(`${API_BASE}/download/sessions`, sessionConfig);
        
        if (!createResponse.data.success) {
            throw new Error(`Failed to create session: ${createResponse.data.error}`);
        }
        
        const session = createResponse.data.data;
        console.log(`   ✅ Session created: ${session.id}`);
        console.log(`   📊 Total files in session: ${session.total_files}`);
        
        // Verify session includes all files (not just undownloaded ones)
        if (session.total_files > 0) {
            console.log(`   ✅ Session includes files (expected behavior with new logic)`);
        } else {
            console.log(`   ⚠️ Session has no files - this might be expected if user has no files`);
        }
        
    } catch (error) {
        console.error('   ❌ Session creation error details:', error.response?.data || error.message);
        throw new Error(`Session creation test failed: ${error.message}`);
    }
}

/**
 * Test file conflict handling with size comparison
 */
async function testFileConflictHandling() {
    try {
        // Create test directory
        if (!fs.existsSync(TEST_DOWNLOAD_PATH)) {
            fs.mkdirSync(TEST_DOWNLOAD_PATH, { recursive: true });
        }
        
        // Create test files with different sizes
        const testDir = path.join(TEST_DOWNLOAD_PATH, 'conflict-test');
        if (!fs.existsSync(testDir)) {
            fs.mkdirSync(testDir, { recursive: true });
        }
        
        // Test file 1: Similar size (should be skipped)
        const testFile1 = path.join(testDir, 'test-similar-size.txt');
        const content1 = 'This is a test file with similar size';
        fs.writeFileSync(testFile1, content1);
        
        // Test file 2: Different size (should get suffix)
        const testFile2 = path.join(testDir, 'test-different-size.txt');
        const content2 = 'This is a test file with very different size - much longer content to create significant size difference';
        fs.writeFileSync(testFile2, content2);
        
        console.log(`   📁 Created test files in: ${testDir}`);
        console.log(`   📄 File 1 size: ${fs.statSync(testFile1).size} bytes`);
        console.log(`   📄 File 2 size: ${fs.statSync(testFile2).size} bytes`);
        console.log(`   ✅ File conflict test setup completed`);
        
    } catch (error) {
        throw new Error(`File conflict test failed: ${error.message}`);
    }
}

/**
 * Test suffix generation for files with different sizes
 */
async function testSuffixGeneration() {
    try {
        // Import the DownloadWorker to test suffix generation directly
        const { DownloadWorker } = await import('./src/services/download-worker.js');
        
        // Create a mock worker instance
        const mockSession = { download_path: TEST_DOWNLOAD_PATH };
        const worker = new DownloadWorker('test-session', mockSession, null);
        
        // Test suffix generation
        const testDir = path.join(TEST_DOWNLOAD_PATH, 'suffix-test');
        if (!fs.existsSync(testDir)) {
            fs.mkdirSync(testDir, { recursive: true });
        }
        
        // Create original file
        const originalFile = path.join(testDir, 'test-file.txt');
        fs.writeFileSync(originalFile, 'Original content');
        
        // Test generateUniqueFileName
        const uniqueName1 = worker.generateUniqueFileName(originalFile);
        const uniqueName2 = worker.generateUniqueFileName(originalFile);
        
        console.log(`   📄 Original file: ${originalFile}`);
        console.log(`   📄 Unique name 1: ${uniqueName1}`);
        console.log(`   📄 Unique name 2: ${uniqueName2}`);
        
        // Verify the names are different and follow the pattern
        if (uniqueName1.includes('_1') && uniqueName2.includes('_2')) {
            console.log(`   ✅ Suffix generation works correctly`);
        } else {
            throw new Error('Suffix generation not working as expected');
        }
        
        // Create the files to test further suffix generation
        fs.writeFileSync(uniqueName1, 'Content 1');
        const uniqueName3 = worker.generateUniqueFileName(originalFile);
        
        if (uniqueName3.includes('_2')) {
            console.log(`   ✅ Suffix generation handles existing suffixed files correctly`);
        } else {
            throw new Error('Suffix generation not handling existing files correctly');
        }
        
    } catch (error) {
        throw new Error(`Suffix generation test failed: ${error.message}`);
    }
}

// Cleanup function
function cleanup() {
    try {
        if (fs.existsSync(TEST_DOWNLOAD_PATH)) {
            fs.rmSync(TEST_DOWNLOAD_PATH, { recursive: true, force: true });
            console.log(`🧹 Cleaned up test directory: ${TEST_DOWNLOAD_PATH}`);
        }
    } catch (error) {
        console.warn(`⚠️ Failed to cleanup test directory: ${error.message}`);
    }
}

// Handle cleanup on exit
process.on('exit', cleanup);
process.on('SIGINT', () => {
    cleanup();
    process.exit(0);
});

// Run tests
main().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    cleanup();
    process.exit(1);
});
