/**
 * Unit test cho logic download mới
 * Test trực tiếp các function đã thay đổi
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { DownloadWorker } from './src/services/download-worker.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const TEST_DIR = path.join(__dirname, 'test-unit-downloads');

async function main() {
    console.log('🧪 Testing download logic units...\n');
    
    try {
        // Setup test directory
        setupTestDirectory();
        
        // Test 1: handleFileNameConflict with similar size (should skip)
        console.log('📋 Test 1: File conflict with similar size (should skip)');
        await testSimilarSizeSkip();
        
        // Test 2: handleFileNameConflict with different size (should create suffix)
        console.log('\n📋 Test 2: File conflict with different size (should create suffix)');
        await testDifferentSizeCreateSuffix();
        
        // Test 3: generateUniqueFileName
        console.log('\n📋 Test 3: Generate unique file names');
        await testGenerateUniqueFileName();
        
        console.log('\n✅ All unit tests passed!');
        
    } catch (error) {
        console.error('❌ Unit test failed:', error.message);
        process.exit(1);
    } finally {
        cleanup();
    }
}

function setupTestDirectory() {
    if (fs.existsSync(TEST_DIR)) {
        fs.rmSync(TEST_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(TEST_DIR, { recursive: true });
    console.log(`📁 Created test directory: ${TEST_DIR}`);
}

async function testSimilarSizeSkip() {
    try {
        // Create mock worker
        const mockSession = { download_path: TEST_DIR };
        const worker = new DownloadWorker('test-session', mockSession, null);
        
        // Create existing file
        const testFile = path.join(TEST_DIR, 'similar-size-test.txt');
        const content = 'This is test content with 50 characters exactly!';
        fs.writeFileSync(testFile, content);
        
        const existingSize = fs.statSync(testFile).size;
        console.log(`   📄 Created existing file: ${testFile} (size: ${existingSize})`);
        
        // Test with similar size (within 10% difference)
        const similarSize = Math.floor(existingSize * 1.05); // 5% larger
        
        try {
            const result = worker.handleFileNameConflict(testFile, similarSize);
            console.log(`   ❌ Expected skip but got result: ${result}`);
            throw new Error('Should have thrown SKIP_DUPLICATE_FILE error');
        } catch (error) {
            if (error.message.startsWith('SKIP_DUPLICATE_FILE')) {
                console.log(`   ✅ Correctly skipped file with similar size`);
                console.log(`   📊 Size comparison: existing=${existingSize}, expected=${similarSize}, diff=${((Math.abs(existingSize - similarSize) / similarSize) * 100).toFixed(1)}%`);
            } else {
                throw error;
            }
        }
        
    } catch (error) {
        throw new Error(`Similar size skip test failed: ${error.message}`);
    }
}

async function testDifferentSizeCreateSuffix() {
    try {
        // Create mock worker
        const mockSession = { download_path: TEST_DIR };
        const worker = new DownloadWorker('test-session', mockSession, null);
        
        // Create existing file
        const testFile = path.join(TEST_DIR, 'different-size-test.txt');
        const content = 'Short content';
        fs.writeFileSync(testFile, content);
        
        const existingSize = fs.statSync(testFile).size;
        console.log(`   📄 Created existing file: ${testFile} (size: ${existingSize})`);
        
        // Test with very different size (more than 10% difference)
        const differentSize = existingSize * 2; // 100% larger
        
        const result = worker.handleFileNameConflict(testFile, differentSize);
        
        if (result !== testFile && result.includes('_1')) {
            console.log(`   ✅ Correctly created suffix file: ${result}`);
            console.log(`   📊 Size comparison: existing=${existingSize}, expected=${differentSize}, diff=${((Math.abs(existingSize - differentSize) / differentSize) * 100).toFixed(1)}%`);
        } else {
            throw new Error(`Expected suffix file but got: ${result}`);
        }
        
    } catch (error) {
        throw new Error(`Different size suffix test failed: ${error.message}`);
    }
}

async function testGenerateUniqueFileName() {
    try {
        // Create mock worker
        const mockSession = { download_path: TEST_DIR };
        const worker = new DownloadWorker('test-session', mockSession, null);
        
        // Create original file
        const originalFile = path.join(TEST_DIR, 'unique-name-test.txt');
        fs.writeFileSync(originalFile, 'Original content');
        
        // Generate first unique name
        const unique1 = worker.generateUniqueFileName(originalFile);

        // Create the first unique file
        fs.writeFileSync(unique1, 'Content 1');

        // Generate second unique name
        const unique2 = worker.generateUniqueFileName(originalFile);

        console.log(`   📄 Original: ${path.basename(originalFile)}`);
        console.log(`   📄 Unique 1: ${path.basename(unique1)}`);
        console.log(`   📄 Unique 2: ${path.basename(unique2)}`);

        // Verify patterns
        if (unique1.includes('_1') && unique2.includes('_2')) {
            console.log(`   ✅ Unique name generation works correctly`);
        } else {
            throw new Error('Unique name generation not following expected pattern');
        }
        
        // Create the second unique file and test again
        fs.writeFileSync(unique2, 'Content 2');
        const unique3 = worker.generateUniqueFileName(originalFile);

        if (unique3.includes('_3')) {
            console.log(`   ✅ Handles existing suffixed files correctly`);
        } else {
            throw new Error(`Expected _3 suffix but got: ${path.basename(unique3)}`);
        }
        
    } catch (error) {
        throw new Error(`Unique filename test failed: ${error.message}`);
    }
}

function cleanup() {
    try {
        if (fs.existsSync(TEST_DIR)) {
            fs.rmSync(TEST_DIR, { recursive: true, force: true });
            console.log(`🧹 Cleaned up test directory`);
        }
    } catch (error) {
        console.warn(`⚠️ Failed to cleanup: ${error.message}`);
    }
}

// Handle cleanup on exit
process.on('exit', cleanup);
process.on('SIGINT', () => {
    cleanup();
    process.exit(0);
});

// Run tests
main().catch(error => {
    console.error('❌ Unit test suite failed:', error.message);
    cleanup();
    process.exit(1);
});
