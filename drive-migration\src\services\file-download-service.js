/**
 * File Download Service
 * Service ch<PERSON>h quản lý download process từ Google Drive
 */

import fs from 'fs';
import path from 'path';
import { SupabaseClient } from '../database/supabase.js';
import { googleDriveAPI } from '../api/google-drive-api.js';
import { DownloadWorker } from './download-worker.js';
import EventEmitter from 'events';

export class FileDownloadService extends EventEmitter {
    constructor() {
        super();
        this.supabase = new SupabaseClient();
        this.workers = new Map(); // sessionId -> DownloadWorker
        this.activeSessions = new Map(); // sessionId -> session data
    }

    /**
     * Tạo download session mới
     */
    async createDownloadSession(config) {
        try {
            const {
                name,
                selectedUsers,
                downloadPath,
                concurrentDownloads = 3,
                maxRetries = 3,
                stopOnError = true,
                continueOnError = false,
                skipMimeTypes = [],
                processingOrder = 'created_at'
            } = config;

            // Validate input
            if (!name || !selectedUsers || !downloadPath) {
                throw new Error('Missing required fields: name, selectedUsers, downloadPath');
            }

            if (!Array.isArray(selectedUsers) || selectedUsers.length === 0) {
                throw new Error('selectedUsers must be a non-empty array');
            }

            // Tạo session trong database
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .insert({
                    name,
                    selected_users: selectedUsers,
                    download_path: downloadPath,
                    concurrent_downloads: concurrentDownloads,
                    max_retries: maxRetries,
                    stop_on_error: stopOnError,
                    continue_on_error: continueOnError,
                    skip_mime_types: skipMimeTypes,
                    processing_order: processingOrder,
                    status: 'pending'
                })
                .select()
                .single();

            if (error) {
                throw new Error(`Failed to create download session: ${error.message}`);
            }

            // Tính toán tổng số files cần download
            await this.calculateSessionStats(session.id, selectedUsers);

            // Lấy lại session sau khi update stats
            const { data: updatedSession, error: updateError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('*')
                .eq('id', session.id)
                .single();

            if (updateError) {
                throw new Error(`Failed to get updated session: ${updateError.message}`);
            }

            console.log(`✅ Created download session: ${session.id} - ${name}`);
            return updatedSession;

        } catch (error) {
            console.error('❌ Error creating download session:', error.message);
            throw error;
        }
    }

    /**
     * Tính toán thống kê session (tổng files, size)
     */
    async calculateSessionStats(sessionId, selectedUsers) {
        try {
            // Get session config to check skip_mime_types and processing_order
            const { data: sessionConfig, error: sessionError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('skip_mime_types, processing_order')
                .eq('id', sessionId)
                .single();

            if (sessionError) {
                throw new Error(`Failed to get session config: ${sessionError.message}`);
            }

            const skipMimeTypes = sessionConfig.skip_mime_types || [];
            const processingOrder = sessionConfig.processing_order || 'created_at';

            // Query files từ scanned_files_cuong cho các users được chọn - fetch in batches to handle large datasets
            // Lấy tất cả files của user được chọn (không filter theo download_status)
            let allFiles = [];
            let hasMore = true;
            let offset = 0;
            const batchSize = 1000; // Process in batches of 1000

            while (hasMore) {
                const { data: batchFiles, error } = await this.supabase.getServiceClient()
                    .from('scanned_files_cuong')
                    .select('id, file_id, name, size, mime_type, full_path, user_email, download_status')
                    .in('user_email', selectedUsers)
                    .range(offset, offset + batchSize - 1);

                if (error) {
                    throw new Error(`Failed to query files: ${error.message}`);
                }

                if (batchFiles && batchFiles.length > 0) {
                    allFiles.push(...batchFiles);

                    // Check if we got a full batch, indicating there might be more
                    hasMore = batchFiles.length === batchSize;
                    offset += batchSize;

                    console.log(`📄 Fetched session stats batch: ${batchFiles.length} files (Total so far: ${allFiles.length})`);
                } else {
                    hasMore = false;
                }
            }

            console.log(`✅ Total files for session stats: ${allFiles.length}`);

            // Filter out skipped MIME types
            let files = allFiles;
            if (skipMimeTypes.length > 0) {
                const originalCount = files.length;
                files = files.filter(file => !skipMimeTypes.includes(file.mime_type));
                console.log(`🚫 Filtered out ${originalCount - files.length} files with skipped MIME types: ${skipMimeTypes.join(', ')}`);
            }

            // Apply processing order
            if (processingOrder === 'user_email') {
                files.sort((a, b) => a.user_email.localeCompare(b.user_email));
            } else if (processingOrder === 'size_asc') {
                files.sort((a, b) => (parseInt(a.size) || 0) - (parseInt(b.size) || 0));
            } else if (processingOrder === 'size_desc') {
                files.sort((a, b) => (parseInt(b.size) || 0) - (parseInt(a.size) || 0));
            }
            // Default 'created_at' order is already applied by the query

            const totalFiles = files.length;
            const totalSize = files.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);

            // Update session stats
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    total_files: totalFiles,
                    total_size: totalSize
                })
                .eq('id', sessionId);

            // No need to create download_items anymore - we work directly with scanned_files_cuong
            // The download worker will process files directly from scanned_files_cuong table
            console.log(`✅ Session prepared with ${totalFiles} files from scanned_files_cuong table`);

            console.log(`📊 Session stats: ${totalFiles} files, ${this.formatFileSize(totalSize)}`);

        } catch (error) {
            console.error('❌ Error calculating session stats:', error.message);
            throw error;
        }
    }

    /**
     * Bắt đầu download session
     */
    async startDownloadSession(sessionId) {
        try {
            // Kiểm tra session tồn tại
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error || !session) {
                throw new Error(`Session not found: ${sessionId}`);
            }

            if (session.status !== 'pending' && session.status !== 'paused') {
                throw new Error(`Cannot start session with status: ${session.status}`);
            }

            // Update session status
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    status: 'running',
                    started_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Tạo và khởi động worker
            const worker = new DownloadWorker(sessionId, session, this.supabase);
            this.workers.set(sessionId, worker);
            this.activeSessions.set(sessionId, session);

            // Listen to worker events
            worker.on('progress', (data) => {
                this.emit('progress', { sessionId, ...data });
            });

            worker.on('completed', (data) => {
                this.workers.delete(sessionId);
                this.activeSessions.delete(sessionId);
                this.emit('completed', { sessionId, ...data });
            });

            worker.on('error', (error) => {
                this.emit('error', { sessionId, error });
            });

            // Bắt đầu download
            worker.start();

            console.log(`🚀 Started download session: ${sessionId}`);
            return { success: true, sessionId };

        } catch (error) {
            console.error('❌ Error starting download session:', error.message);
            throw error;
        }
    }

    /**
     * Pause download session
     */
    async pauseDownloadSession(sessionId) {
        try {
            const worker = this.workers.get(sessionId);
            if (worker) {
                await worker.pause();
            }

            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({ status: 'paused' })
                .eq('id', sessionId);

            console.log(`⏸️ Paused download session: ${sessionId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Error pausing download session:', error.message);
            throw error;
        }
    }

    /**
     * Cancel download session
     */
    async cancelDownloadSession(sessionId) {
        try {
            const worker = this.workers.get(sessionId);
            if (worker) {
                await worker.cancel();
                this.workers.delete(sessionId);
                this.activeSessions.delete(sessionId);
            }

            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    status: 'cancelled',
                    completed_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            console.log(`❌ Cancelled download session: ${sessionId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Error cancelling download session:', error.message);
            throw error;
        }
    }

    /**
     * Lấy thông tin session
     */
    async getSessionInfo(sessionId) {
        try {
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_session_stats')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error) {
                throw new Error(`Failed to get session info: ${error.message}`);
            }

            return session;

        } catch (error) {
            console.error('❌ Error getting session info:', error.message);
            throw error;
        }
    }

    /**
     * Lấy danh sách sessions
     */
    async listSessions(limit = 50) {
        try {
            const { data: sessions, error } = await this.supabase.getServiceClient()
                .from('download_session_stats')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to list sessions: ${error.message}`);
            }

            return sessions;

        } catch (error) {
            console.error('❌ Error listing sessions:', error.message);
            throw error;
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        for (const [sessionId, worker] of this.workers) {
            try {
                await worker.cancel();
            } catch (error) {
                console.error(`Error cleaning up worker ${sessionId}:`, error.message);
            }
        }
        this.workers.clear();
        this.activeSessions.clear();
    }
}
