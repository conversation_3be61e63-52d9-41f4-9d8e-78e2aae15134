/**
 * Comprehensive test cho logic copy file gi<PERSON><PERSON> các account
 * Test cases:
 * 1. File được copy đúng từ account A sang B
 * 2. Registry được update đúng
 * 3. <PERSON><PERSON><PERSON> trúc thư mục được giữ nguyên
 * 4. Performance improvement khi copy vs download
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { FilePathRegistry } from './src/services/file-path-registry.js';
import { DownloadWorker } from './src/services/download-worker.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const TEST_DIR = path.join(__dirname, 'test-file-copy');
const ACCOUNT_A = '<EMAIL>';
const ACCOUNT_B = '<EMAIL>';

async function main() {
    console.log('🧪 Testing file copy logic between accounts...\n');
    
    try {
        // Setup test environment
        setupTestEnvironment();
        
        // Test 1: Registry functionality
        console.log('📋 Test 1: FilePathRegistry functionality');
        await testRegistryFunctionality();
        
        // Test 2: File copy between accounts
        console.log('\n📋 Test 2: File copy between accounts');
        await testFileCopyBetweenAccounts();
        
        // Test 3: Directory structure preservation
        console.log('\n📋 Test 3: Directory structure preservation');
        await testDirectoryStructurePreservation();
        
        // Test 4: Performance comparison
        console.log('\n📋 Test 4: Performance comparison (copy vs download simulation)');
        await testPerformanceComparison();
        
        // Test 5: Edge cases
        console.log('\n📋 Test 5: Edge cases and error handling');
        await testEdgeCases();
        
        console.log('\n✅ All file copy tests passed!');
        
    } catch (error) {
        console.error('❌ File copy test failed:', error.message);
        process.exit(1);
    } finally {
        cleanup();
    }
}

function setupTestEnvironment() {
    if (fs.existsSync(TEST_DIR)) {
        fs.rmSync(TEST_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(TEST_DIR, { recursive: true });
    console.log(`📁 Created test directory: ${TEST_DIR}`);
}

async function testRegistryFunctionality() {
    try {
        const registry = new FilePathRegistry();
        
        // Test file registration
        const testPath = '/30 bài zodiac/[Bài 02] test file.docx';
        const localPath = path.join(TEST_DIR, ACCOUNT_A, '30 bài zodiac', '[Bài 02] test file.docx');
        
        // Create test file
        fs.mkdirSync(path.dirname(localPath), { recursive: true });
        fs.writeFileSync(localPath, 'Test content for account A');
        
        // Register file
        registry.registerDownloadedFile(testPath, ACCOUNT_A, localPath, 1000);
        
        // Test registry queries
        const isDownloaded = registry.isFileDownloaded(testPath);
        const fileInfo = registry.getDownloadedFileInfo(testPath);
        const canCopy = registry.canCopyFromExistingAccount(testPath, ACCOUNT_B);
        
        console.log(`   ✅ File registration: ${isDownloaded ? 'SUCCESS' : 'FAILED'}`);
        console.log(`   ✅ File info retrieval: ${fileInfo ? 'SUCCESS' : 'FAILED'}`);
        console.log(`   ✅ Copy availability check: ${canCopy ? 'SUCCESS' : 'FAILED'}`);
        
        // Test stats
        const stats = registry.getStats();
        console.log(`   📊 Registry stats: ${stats.totalTrackedFiles} files tracked`);
        
        if (!isDownloaded || !fileInfo || !canCopy) {
            throw new Error('Registry functionality test failed');
        }
        
    } catch (error) {
        throw new Error(`Registry functionality test failed: ${error.message}`);
    }
}

async function testFileCopyBetweenAccounts() {
    try {
        // Create mock worker
        const mockSession = { download_path: TEST_DIR };
        const worker = new DownloadWorker('test-session', mockSession, null);
        
        // Override updateDownloadItemStatus and supabase calls for testing
        worker.updateDownloadItemStatus = async () => ({ data: null, error: null });
        worker.supabase = {
            getServiceClient: () => ({
                from: () => ({
                    update: () => ({
                        eq: () => ({ data: null, error: null })
                    })
                })
            })
        };
        
        const registry = new FilePathRegistry();
        
        // Setup: Create file in Account A
        const testPath = '/shared folder/important document.pdf';
        const sourceLocalPath = path.join(TEST_DIR, ACCOUNT_A, 'shared folder', 'important document.pdf');
        
        fs.mkdirSync(path.dirname(sourceLocalPath), { recursive: true });
        const testContent = 'This is an important document that should be copied, not downloaded again.';
        fs.writeFileSync(sourceLocalPath, testContent);
        
        // Register file in Account A
        registry.registerDownloadedFile(testPath, ACCOUNT_A, sourceLocalPath, testContent.length);
        
        // Test: Copy to Account B
        const downloadItem = {
            id: 'test-item-123',
            file_name: 'important document.pdf',
            file_path: testPath,
            user_email: ACCOUNT_B,
            file_size: testContent.length,
            scanned_file_id: 'scanned-123'
        };
        
        const sourceFileInfo = registry.canCopyFromExistingAccount(testPath, ACCOUNT_B);
        
        if (!sourceFileInfo) {
            throw new Error('Source file info not found for copy operation');
        }
        
        const copyResult = await worker.copyFileFromExistingAccount(downloadItem, sourceFileInfo);
        
        // Verify copy result
        if (!copyResult.success || !copyResult.copied) {
            throw new Error('Copy operation did not return success');
        }
        
        // Verify target file exists and has correct content
        const targetContent = fs.readFileSync(copyResult.localPath, 'utf8');
        if (targetContent !== testContent) {
            throw new Error('Copied file content does not match source');
        }
        
        console.log(`   ✅ File copied successfully from ${ACCOUNT_A} to ${ACCOUNT_B}`);
        console.log(`   📄 Source: ${sourceLocalPath}`);
        console.log(`   📄 Target: ${copyResult.localPath}`);
        console.log(`   📊 Content verified: ${targetContent.length} bytes`);
        
    } catch (error) {
        throw new Error(`File copy test failed: ${error.message}`);
    }
}

async function testDirectoryStructurePreservation() {
    try {
        const registry = new FilePathRegistry();
        
        // Test complex directory structure
        const complexPath = '/Projects/2024/Q1/Marketing/Campaigns/Social Media/Facebook/Images/banner.jpg';
        const sourceDir = path.join(TEST_DIR, ACCOUNT_A, 'Projects', '2024', 'Q1', 'Marketing', 'Campaigns', 'Social Media', 'Facebook', 'Images');
        const sourceFile = path.join(sourceDir, 'banner.jpg');
        
        // Create complex directory structure
        fs.mkdirSync(sourceDir, { recursive: true });
        fs.writeFileSync(sourceFile, 'Fake image content');
        
        // Register file
        registry.registerDownloadedFile(complexPath, ACCOUNT_A, sourceFile, 100);
        
        // Simulate copy to Account B
        const targetDir = path.join(TEST_DIR, ACCOUNT_B, 'Projects', '2024', 'Q1', 'Marketing', 'Campaigns', 'Social Media', 'Facebook', 'Images');
        const targetFile = path.join(targetDir, 'banner.jpg');
        
        // Create target directory structure
        fs.mkdirSync(targetDir, { recursive: true });
        fs.copyFileSync(sourceFile, targetFile);
        
        // Verify directory structure is preserved
        const sourceExists = fs.existsSync(sourceFile);
        const targetExists = fs.existsSync(targetFile);
        const structurePreserved = path.relative(path.join(TEST_DIR, ACCOUNT_A), sourceFile) === 
                                   path.relative(path.join(TEST_DIR, ACCOUNT_B), targetFile);
        
        console.log(`   ✅ Source file exists: ${sourceExists}`);
        console.log(`   ✅ Target file exists: ${targetExists}`);
        console.log(`   ✅ Directory structure preserved: ${structurePreserved}`);
        
        if (!sourceExists || !targetExists || !structurePreserved) {
            throw new Error('Directory structure preservation test failed');
        }
        
    } catch (error) {
        throw new Error(`Directory structure test failed: ${error.message}`);
    }
}

async function testPerformanceComparison() {
    try {
        // Simulate performance comparison
        const fileSize = 1024 * 1024; // 1MB
        const testContent = 'x'.repeat(fileSize);
        
        // Test 1: Copy operation
        const copyStartTime = Date.now();
        const sourceFile = path.join(TEST_DIR, 'perf-source.txt');
        const targetFile = path.join(TEST_DIR, 'perf-target.txt');
        
        fs.writeFileSync(sourceFile, testContent);
        fs.copyFileSync(sourceFile, targetFile);
        
        const copyTime = Date.now() - copyStartTime;
        
        // Test 2: Simulate download operation (write operation)
        const downloadStartTime = Date.now();
        const downloadFile = path.join(TEST_DIR, 'perf-download.txt');
        
        fs.writeFileSync(downloadFile, testContent);
        
        const downloadTime = Date.now() - downloadStartTime;
        
        const speedImprovement = downloadTime > 0 ? ((downloadTime - copyTime) / downloadTime * 100).toFixed(1) : 0;
        
        console.log(`   📊 Copy operation: ${copyTime}ms`);
        console.log(`   📊 Download simulation: ${downloadTime}ms`);
        console.log(`   📊 Speed improvement: ${speedImprovement}% faster`);
        console.log(`   ✅ Performance test completed`);
        
        // Cleanup performance test files
        [sourceFile, targetFile, downloadFile].forEach(file => {
            if (fs.existsSync(file)) fs.unlinkSync(file);
        });
        
    } catch (error) {
        throw new Error(`Performance test failed: ${error.message}`);
    }
}

async function testEdgeCases() {
    try {
        const registry = new FilePathRegistry();
        
        // Test 1: Non-existent source file
        registry.registerDownloadedFile('/fake/path.txt', ACCOUNT_A, '/non/existent/file.txt', 100);
        const canCopyNonExistent = registry.canCopyFromExistingAccount('/fake/path.txt', ACCOUNT_B);
        
        console.log(`   ✅ Non-existent source file handled: ${canCopyNonExistent === null ? 'SUCCESS' : 'FAILED'}`);
        
        // Test 2: Same account check
        const testPath = '/same/account/test.txt';
        const localPath = path.join(TEST_DIR, 'same-account-test.txt');
        fs.writeFileSync(localPath, 'test');
        
        registry.registerDownloadedFile(testPath, ACCOUNT_A, localPath, 100);
        const canCopySameAccount = registry.canCopyFromExistingAccount(testPath, ACCOUNT_A);
        
        console.log(`   ✅ Same account check: ${canCopySameAccount === null ? 'SUCCESS' : 'FAILED'}`);
        
        // Test 3: Path normalization
        const path1 = '/Test/Path/File.txt';
        const path2 = '\\Test\\Path\\File.txt';
        const path3 = '/Test/Path/File.txt/'; // with trailing slash

        registry.registerDownloadedFile(path1, ACCOUNT_A, localPath, 100);
        const normalized1 = registry.isFileDownloaded(path2);
        const normalized2 = registry.isFileDownloaded(path3);

        console.log(`   🔍 Debug path normalization:`);
        console.log(`      path1: "${path1}" -> normalized: "${registry.normalizePath(path1)}"`);
        console.log(`      path2: "${path2}" -> normalized: "${registry.normalizePath(path2)}"`);
        console.log(`      path3: "${path3}" -> normalized: "${registry.normalizePath(path3)}"`);
        console.log(`      normalized1 result: ${normalized1}`);
        console.log(`      normalized2 result: ${normalized2}`);

        console.log(`   ✅ Path normalization: ${normalized1 && normalized2 ? 'SUCCESS' : 'FAILED'}`);

        if (canCopyNonExistent !== null || canCopySameAccount !== null || !normalized1 || !normalized2) {
            throw new Error('Edge cases test failed');
        }
        
    } catch (error) {
        throw new Error(`Edge cases test failed: ${error.message}`);
    }
}

function cleanup() {
    try {
        if (fs.existsSync(TEST_DIR)) {
            fs.rmSync(TEST_DIR, { recursive: true, force: true });
            console.log(`🧹 Cleaned up test directory`);
        }
    } catch (error) {
        console.warn(`⚠️ Failed to cleanup: ${error.message}`);
    }
}

// Handle cleanup on exit
process.on('exit', cleanup);
process.on('SIGINT', () => {
    cleanup();
    process.exit(0);
});

// Run tests
main().catch(error => {
    console.error('❌ File copy test suite failed:', error.message);
    cleanup();
    process.exit(1);
});
