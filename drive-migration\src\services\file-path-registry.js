/**
 * FilePathRegistry - Track files đã download để tránh download trùng lặp
 * 
 * <PERSON><PERSON><PERSON> mapping giữa full_path và account đã download file đó
 * Khi file tồn tại ở nhiều account, chỉ cần copy thay vì download lại
 */

import fs from 'fs';
import path from 'path';

export class FilePathRegistry {
    constructor() {
        // Map: full_path -> { userEmail, localPath, fileSize, downloadedAt }
        this.pathRegistry = new Map();
        
        // Map: userEmail -> Set of full_paths (for quick lookup by user)
        this.userPathsRegistry = new Map();
        
        this.stats = {
            totalTrackedFiles: 0,
            totalCopiedFiles: 0,
            totalSavedDownloads: 0
        };
    }

    /**
     * Đăng ký file đã được download
     */
    registerDownloadedFile(fullPath, userEmail, localPath, fileSize = 0) {
        try {
            // Normalize full_path để đảm bảo consistency
            const normalizedPath = this.normalizePath(fullPath);
            
            const fileInfo = {
                userEmail,
                localPath,
                fileSize,
                downloadedAt: new Date().toISOString(),
                method: 'download'
            };
            
            // Lưu vào path registry
            this.pathRegistry.set(normalizedPath, fileInfo);
            
            // Lưu vào user registry
            if (!this.userPathsRegistry.has(userEmail)) {
                this.userPathsRegistry.set(userEmail, new Set());
            }
            this.userPathsRegistry.get(userEmail).add(normalizedPath);
            
            this.stats.totalTrackedFiles++;
            
            console.log(`📝 Registered downloaded file: ${normalizedPath} -> ${userEmail}`);
            
        } catch (error) {
            console.error(`❌ Failed to register downloaded file: ${error.message}`);
        }
    }

    /**
     * Đăng ký file đã được copy từ account khác
     */
    registerCopiedFile(fullPath, targetUserEmail, targetLocalPath, sourceUserEmail, fileSize = 0) {
        try {
            const normalizedPath = this.normalizePath(fullPath);
            
            const fileInfo = {
                userEmail: targetUserEmail,
                localPath: targetLocalPath,
                fileSize,
                downloadedAt: new Date().toISOString(),
                method: 'copy',
                copiedFrom: sourceUserEmail
            };
            
            // Lưu vào path registry (update với target user)
            this.pathRegistry.set(normalizedPath, fileInfo);
            
            // Lưu vào user registry
            if (!this.userPathsRegistry.has(targetUserEmail)) {
                this.userPathsRegistry.set(targetUserEmail, new Set());
            }
            this.userPathsRegistry.get(targetUserEmail).add(normalizedPath);
            
            this.stats.totalCopiedFiles++;
            
            console.log(`📝 Registered copied file: ${normalizedPath} -> ${targetUserEmail} (from ${sourceUserEmail})`);
            
        } catch (error) {
            console.error(`❌ Failed to register copied file: ${error.message}`);
        }
    }

    /**
     * Kiểm tra file đã được download chưa
     */
    isFileDownloaded(fullPath) {
        const normalizedPath = this.normalizePath(fullPath);
        return this.pathRegistry.has(normalizedPath);
    }

    /**
     * Lấy thông tin file đã download (nếu có)
     */
    getDownloadedFileInfo(fullPath) {
        const normalizedPath = this.normalizePath(fullPath);
        return this.pathRegistry.get(normalizedPath) || null;
    }

    /**
     * Kiểm tra file có thể copy từ account khác không
     */
    canCopyFromExistingAccount(fullPath, currentUserEmail) {
        const normalizedPath = this.normalizePath(fullPath);
        const fileInfo = this.pathRegistry.get(normalizedPath);
        
        if (!fileInfo) {
            return null; // File chưa được download
        }
        
        if (fileInfo.userEmail === currentUserEmail) {
            return null; // File đã có ở current user
        }
        
        // Kiểm tra file source vẫn tồn tại
        if (!fs.existsSync(fileInfo.localPath)) {
            console.warn(`⚠️ Source file not found: ${fileInfo.localPath}`);
            return null;
        }
        
        return fileInfo; // Có thể copy từ account khác
    }

    /**
     * Lấy danh sách files của một user
     */
    getUserFiles(userEmail) {
        const userPaths = this.userPathsRegistry.get(userEmail);
        if (!userPaths) {
            return [];
        }
        
        return Array.from(userPaths).map(fullPath => ({
            fullPath,
            ...this.pathRegistry.get(fullPath)
        }));
    }

    /**
     * Normalize path để đảm bảo consistency
     */
    normalizePath(fullPath) {
        // Normalize separators first, then remove leading/trailing slashes
        return fullPath
            .replace(/\\/g, '/') // Convert backslashes to forward slashes first
            .replace(/^\/+|\/+$/g, '') // Remove leading/trailing slashes
            .toLowerCase(); // Case insensitive comparison
    }

    /**
     * Lấy thống kê registry
     */
    getStats() {
        return {
            ...this.stats,
            totalUniqueFiles: this.pathRegistry.size,
            totalUsers: this.userPathsRegistry.size,
            savedDownloadPercentage: this.stats.totalTrackedFiles > 0 
                ? ((this.stats.totalCopiedFiles / this.stats.totalTrackedFiles) * 100).toFixed(1)
                : 0
        };
    }

    /**
     * Clear registry (for testing)
     */
    clear() {
        this.pathRegistry.clear();
        this.userPathsRegistry.clear();
        this.stats = {
            totalTrackedFiles: 0,
            totalCopiedFiles: 0,
            totalSavedDownloads: 0
        };
    }

    /**
     * Export registry data (for debugging)
     */
    exportData() {
        return {
            pathRegistry: Object.fromEntries(this.pathRegistry),
            userPathsRegistry: Object.fromEntries(
                Array.from(this.userPathsRegistry.entries()).map(([user, paths]) => [
                    user, 
                    Array.from(paths)
                ])
            ),
            stats: this.stats
        };
    }
}

// Singleton instance
export const filePathRegistry = new FilePathRegistry();
