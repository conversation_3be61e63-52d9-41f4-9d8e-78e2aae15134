/**
 * Test script for the new download workflow using scanned_files_cuong
 * This script tests the updated download functionality
 */

import axios from 'axios';
import path from 'path';
import fs from 'fs';

const API_BASE = 'http://localhost:3001/api';
const TEST_DOWNLOAD_PATH = path.resolve('./test-downloads-cuong');

async function testNewDownloadWorkflow() {
    console.log('🧪 Testing New Download Workflow with scanned_files_cuong');
    console.log('=' .repeat(60));

    try {
        // 1. Test API endpoints
        console.log('1️⃣ Testing updated API endpoints...');
        
        // Test get users (should now use scanned_files_cuong)
        console.log('   📋 Getting users from scanned_files_cuong...');
        const usersResponse = await axios.get(`${API_BASE}/download/users`);
        console.log(`   ✅ Found ${usersResponse.data.data.length} users with files`);
        
        const users = usersResponse.data.data;
        const usersWithFiles = users.filter(user => user.fileCount > 0);
        
        if (usersWithFiles.length === 0) {
            console.log('   ⚠️ No users with files found. Make sure scanned_files_cuong is populated.');
            console.log('   💡 Run the populate_scanned_files_cuong.sql migration first.');
            return;
        }

        // Display user statistics
        console.log('   📊 User Statistics:');
        usersWithFiles.slice(0, 5).forEach(user => {
            console.log(`      • ${user.primary_email}: ${user.fileCount} files, ${formatFileSize(user.totalSize)}`);
        });

        // 2. Test undownloaded files endpoint
        const testUser = usersWithFiles[0];
        console.log(`\n2️⃣ Testing undownloaded files for: ${testUser.primary_email}`);
        
        const undownloadedResponse = await axios.get(
            `${API_BASE}/download/users/${encodeURIComponent(testUser.primary_email)}/undownloaded-files`
        );
        
        console.log(`   ✅ Found ${undownloadedResponse.data.data.length} undownloaded files`);
        
        if (undownloadedResponse.data.data.length > 0) {
            const sampleFile = undownloadedResponse.data.data[0];
            console.log(`   📄 Sample file: ${sampleFile.name} (${formatFileSize(sampleFile.size)})`);
        }

        // 3. Create download session
        console.log('\n3️⃣ Creating download session...');
        
        // Ensure test download directory exists
        if (!fs.existsSync(TEST_DOWNLOAD_PATH)) {
            fs.mkdirSync(TEST_DOWNLOAD_PATH, { recursive: true });
        }
        
        const sessionConfig = {
            name: `Test New Workflow - ${new Date().toISOString()}`,
            selectedUsers: [testUser.primary_email],
            downloadPath: TEST_DOWNLOAD_PATH,
            concurrentDownloads: 2,
            maxRetries: 2,
            skipMimeTypes: ['application/vnd.google-apps.folder', 'application/vnd.google-apps.shortcut'],
            processingOrder: 'created_at'
        };
        
        console.log('   📝 Session config:', JSON.stringify(sessionConfig, null, 2));
        
        const createResponse = await axios.post(`${API_BASE}/download/sessions`, sessionConfig);
        
        if (!createResponse.data.success) {
            throw new Error(`Failed to create session: ${createResponse.data.error}`);
        }
        
        const session = createResponse.data.data;
        console.log(`   ✅ Created session: ${session.id}`);
        console.log(`   📊 Session stats: ${session.total_files} files, ${formatFileSize(session.total_size)}`);

        // 4. Start download session
        console.log('\n4️⃣ Starting download session...');
        
        const startResponse = await axios.post(`${API_BASE}/download/sessions/${session.id}/start`);
        
        if (!startResponse.data.success) {
            throw new Error(`Failed to start session: ${startResponse.data.error}`);
        }
        
        console.log('   ✅ Download session started successfully');
        console.log('   ⏳ Session is now running in the background');
        
        // 5. Monitor progress for a short time
        console.log('\n5️⃣ Monitoring progress...');
        
        let monitorCount = 0;
        const maxMonitorTime = 30; // Monitor for 30 seconds
        
        const monitorInterval = setInterval(async () => {
            try {
                const progressResponse = await axios.get(`${API_BASE}/download/sessions/${session.id}`);
                const sessionData = progressResponse.data.data;
                
                console.log(`   📈 Progress: ${sessionData.downloaded_files}/${sessionData.total_files} files downloaded`);
                console.log(`   📊 Status: ${sessionData.status}`);
                
                if (sessionData.status === 'completed' || sessionData.status === 'failed') {
                    clearInterval(monitorInterval);
                    console.log(`   🏁 Session ${sessionData.status}`);
                    
                    if (sessionData.status === 'completed') {
                        console.log(`   ✅ Successfully downloaded ${sessionData.downloaded_files} files`);
                        console.log(`   📁 Files saved to: ${TEST_DOWNLOAD_PATH}`);
                    }
                }
                
                monitorCount++;
                if (monitorCount >= maxMonitorTime) {
                    clearInterval(monitorInterval);
                    console.log('   ⏰ Monitoring timeout reached');
                }
                
            } catch (error) {
                console.error('   ❌ Error monitoring progress:', error.message);
            }
        }, 1000);

        console.log('\n✅ New download workflow test completed successfully!');
        console.log('🔍 Key changes verified:');
        console.log('   • Users fetched from scanned_files_cuong table');
        console.log('   • No download_items table used');
        console.log('   • Direct updates to scanned_files_cuong records');
        console.log('   • File path registry checking implemented');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('   Response data:', error.response.data);
        }
        process.exit(1);
    }
}

function formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
testNewDownloadWorkflow().catch(console.error);
