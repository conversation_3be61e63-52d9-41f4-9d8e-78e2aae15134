# File Copy Logic Improvements

## Summary of Changes

This document outlines the improvements made to the file copy logic in the download worker to better handle shared files and avoid redundant downloads.

## Issues Fixed

### 1. ✅ Duplicate File Warning Message
**Problem**: Duplicate files were being logged as errors instead of warnings.

**Solution**: Changed the log level from `console.error` to `console.warn` for SKIP_DUPLICATE_FILE messages.

**Location**: `src/services/download-worker.js` line 275-280

**Before**:
```javascript
console.error(`❌ Failed to download ${downloadItem.name}:`, error.message);
```

**After**:
```javascript
if (error.message.startsWith('SKIP_DUPLICATE_FILE')) {
    console.warn(`⚠️ Skipping duplicate file: ${downloadItem.name} - ${error.message}`);
    // ... handle skipping logic
    return;
}
console.error(`❌ Failed to download ${downloadItem.name}:`, error.message);
```

### 2. ✅ Corrected File Copy Logic Understanding
**Problem**: Initial misunderstanding of the requirement - the logic should prioritize local file path registry, not database fields.

**Correct Approach**: The existing `FilePathRegistry` logic is actually correct and should be maintained. The priority should be:

1. **First Priority**: Check local file path registry (in-memory) for files already downloaded in current session
2. **Second Priority**: Check if same file exists in another user's local path (from registry) and copy it
3. **Do NOT check**: Database `download_status` or `local_path` fields in Supabase table

## Correct Implementation Details

### Local File Path Registry Logic (Maintained)

The existing logic using `FilePathRegistry` is correct and follows the right priority:

#### Step 1: Check Current User's Downloaded Files
```javascript
const existingFileInfo = filePathRegistry.getDownloadedFileInfo(downloadItem.full_path);

if (existingFileInfo &&
    existingFileInfo.fileSize === downloadItem.size &&
    fs.existsSync(existingFileInfo.localPath)) {

    console.log(`⏭️ File already downloaded, skipping: ${downloadItem.name}`);
    // Update status and return
}
```

#### Step 2: Check Other Users' Downloaded Files (Copy Logic)
```javascript
const copyableFileInfo = filePathRegistry.canCopyFromExistingAccount(
    downloadItem.full_path,
    downloadItem.user_email
);

if (copyableFileInfo) {
    console.log(`🔄 File exists in another account, copying instead of downloading...`);
    const copyResult = await this.copyFileFromExistingAccount(downloadItem, copyableFileInfo);
    return copyResult;
}
```

### Why This Approach is Correct

1. **In-Memory Registry**: Fast lookup for current session downloads
2. **Local File Verification**: Always checks if files actually exist on disk
3. **Cross-User Copy**: Efficiently copies files between user folders
4. **Session-Based**: Works within the current download session context
5. **No Database Dependency**: Doesn't rely on potentially stale database status fields

## Database Examples Found

### Shared Files in Database
The analysis found examples of files that exist across multiple users:

#### Example 1: "teca" Folder
- **File**: `teca` (folder)
- **Users**: `<EMAIL>`, `<EMAIL>`
- **Type**: Different file_ids but same name/size (similar files)
- **Permissions**: Shows extensive sharing permissions across the organization

#### Example 2: "Bảng tính chưa có tiêu đề" (Untitled Spreadsheet)
- **File**: Default Google Sheets
- **Users**: `<EMAIL>`, `<EMAIL>`
- **Type**: Different file_ids but same name/size
- **Size**: 1024 bytes each

## Current Database State

- **Total Files**: 1000 files scanned
- **Downloaded Files**: 0 (no files have local_path yet)
- **Shared Files by file_id**: 0 (no true shared files found)
- **Similar Files**: 2 groups found

## Testing

Created test scripts to verify the new logic:
- `query-shared-files.js`: Analyzes database for shared files
- `test-database-copy-logic.js`: Tests the new copy detection logic

## Future Improvements

1. **Performance**: Add database indexes on commonly queried fields
2. **Metrics**: Track copy success rates and time savings
3. **Cleanup**: Remove dependency on `FilePathRegistry` once fully migrated
4. **Validation**: Add file hash comparison for better duplicate detection

## Key Principles Confirmed

### ✅ Correct Priority Order
1. **Local File Path Registry First**: Check in-memory registry for current session downloads
2. **Cross-User Copy Second**: Check if another user has the file locally and copy it
3. **Never Check Database Status**: Don't rely on `download_status` or `local_path` in Supabase

### ✅ Why This Approach Works
- **Performance**: In-memory lookups are faster than database queries
- **Reliability**: Local file system is the source of truth
- **Session Context**: Works within the current download session
- **Real-time**: Reflects actual file availability on disk

## Usage

The corrected logic works as follows when downloading files:

1. **Check Registry**: Is this file already downloaded by current user in this session?
2. **Check Cross-User**: Is this file available in another user's local folder?
3. **Copy if Available**: Copy from existing location instead of downloading
4. **Download if Not**: Only download if no local copy exists

### Benefits
- ✅ Avoids redundant downloads within session
- ✅ Efficiently copies files between user folders
- ✅ Fast in-memory lookups
- ✅ Reliable local file verification
- ✅ Reduced bandwidth usage for shared files

## Important Note

**Do NOT check database fields** like `download_status` or `local_path` in the Supabase table for copy logic. The local file path registry and actual file system are the authoritative sources for determining what files are available for copying.
