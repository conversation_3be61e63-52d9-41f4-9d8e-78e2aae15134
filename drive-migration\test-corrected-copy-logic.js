import { FilePathRegistry } from './src/services/file-path-registry.js';
import fs from 'fs';
import path from 'path';

/**
 * Test the corrected file copy logic using FilePathRegistry
 */
async function testCorrectedCopyLogic() {
    try {
        console.log('🧪 Testing corrected file copy logic with FilePathRegistry...');
        
        const registry = new FilePathRegistry();
        const testDir = './test-copy-logic';
        
        // Clean up and create test directory
        if (fs.existsSync(testDir)) {
            fs.rmSync(testDir, { recursive: true });
        }
        fs.mkdirSync(testDir, { recursive: true });
        
        // Test scenario: Two users with shared files
        const userA = '<EMAIL>';
        const userB = '<EMAIL>';
        const sharedFilePath = '/shared/document.pdf';
        
        // Step 1: User A downloads a file
        console.log('\n📥 Step 1: User A downloads a file');
        const userALocalPath = path.join(testDir, 'userA', 'shared', 'document.pdf');
        fs.mkdirSync(path.dirname(userALocalPath), { recursive: true });
        fs.writeFileSync(userALocalPath, 'Test content for shared document');
        
        // Register the downloaded file for User A
        registry.registerDownloadedFile(sharedFilePath, userA, userALocalPath, 1000);
        
        console.log(`✅ User A downloaded: ${sharedFilePath}`);
        console.log(`   Local path: ${userALocalPath}`);
        
        // Step 2: Check if User A already has the file (should find it)
        console.log('\n🔍 Step 2: Check if User A already has the file');
        const userAExisting = registry.getDownloadedFileInfo(sharedFilePath);
        
        if (userAExisting && userAExisting.userEmail === userA) {
            console.log(`✅ Found existing file for User A: ${userAExisting.localPath}`);
        } else {
            console.log(`❌ Should have found existing file for User A`);
        }
        
        // Step 3: User B wants to download the same file - check for copy opportunity
        console.log('\n🔄 Step 3: User B wants the same file - check copy opportunity');
        const copyableForUserB = registry.canCopyFromExistingAccount(sharedFilePath, userB);
        
        if (copyableForUserB) {
            console.log(`✅ User B can copy from User A:`);
            console.log(`   Source user: ${copyableForUserB.userEmail}`);
            console.log(`   Source path: ${copyableForUserB.localPath}`);
            console.log(`   File size: ${copyableForUserB.fileSize}`);
            
            // Simulate the copy operation
            const userBLocalPath = path.join(testDir, 'userB', 'shared', 'document.pdf');
            fs.mkdirSync(path.dirname(userBLocalPath), { recursive: true });
            fs.copyFileSync(copyableForUserB.localPath, userBLocalPath);
            
            // Register the copied file for User B
            registry.registerCopiedFile(sharedFilePath, userB, userBLocalPath, userA, 1000);
            
            console.log(`✅ Successfully copied file for User B: ${userBLocalPath}`);
        } else {
            console.log(`❌ Should have found copyable file for User B`);
        }
        
        // Step 4: Verify both users have the file
        console.log('\n📊 Step 4: Verify registry state');
        const userAFiles = registry.getUserFiles(userA);
        const userBFiles = registry.getUserFiles(userB);
        
        console.log(`User A files: ${userAFiles.length}`);
        userAFiles.forEach(file => {
            console.log(`  - ${file.fullPath} (${file.method || 'download'})`);
        });
        
        console.log(`User B files: ${userBFiles.length}`);
        userBFiles.forEach(file => {
            console.log(`  - ${file.fullPath} (${file.method || 'download'})`);
        });
        
        // Step 5: Test edge cases
        console.log('\n🧪 Step 5: Test edge cases');
        
        // Test same user check (should return null)
        const sameUserCheck = registry.canCopyFromExistingAccount(sharedFilePath, userA);
        console.log(`Same user check (should be null): ${sameUserCheck === null ? 'PASS' : 'FAIL'}`);

        // The issue is that the registry stores the last user who accessed the file
        // Let's check what's actually in the registry
        const currentFileInfo = registry.getDownloadedFileInfo(sharedFilePath);
        console.log(`   Current file owner in registry: ${currentFileInfo?.userEmail}`);
        console.log(`   Checking against user: ${userA}`);
        
        // Test non-existent file
        const nonExistentCheck = registry.canCopyFromExistingAccount('/non/existent/file.txt', userB);
        console.log(`Non-existent file check (should be null): ${nonExistentCheck === null ? 'PASS' : 'FAIL'}`);
        
        // Step 6: Show registry statistics
        console.log('\n📈 Step 6: Registry Statistics');
        console.log(`Total tracked files: ${registry.stats.totalTrackedFiles}`);
        console.log(`Total copied files: ${registry.stats.totalCopiedFiles}`);
        console.log(`Total saved downloads: ${registry.stats.totalSavedDownloads}`);
        
        // Clean up
        fs.rmSync(testDir, { recursive: true });
        
        console.log('\n✅ Corrected copy logic test completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testCorrectedCopyLogic()
    .then(() => {
        console.log('\n🎉 Test completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test script failed:', error.message);
        process.exit(1);
    });
